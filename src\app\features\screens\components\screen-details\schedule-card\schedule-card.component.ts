import { Component, EventEmitter, Input, OnInit, OnChanges, SimpleChanges, Output, AfterViewInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PlaylistScheduleBase } from '../../../../../models/screen.model';
import { PlaylistService } from '../../../../playlists/services/playlist.service';

interface CalendarEvent {
  id: string;
  title: string;
  startTime: string;
  endTime: string;
  days: string[];
  priority: number;
  schedule: PlaylistScheduleBase;
}

@Component({
  selector: 'app-schedule-card',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="bg-white rounded-lg shadow-md overflow-hidden h-screen">
      <!-- Header -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-medium text-gray-900">Schedule Calendar</h2>
          <div class="flex items-center gap-4">
            <button
              (click)="onAddSchedule.emit()"
              class="flex items-center text-sm text-blue-600 hover:text-blue-700"
            >
              <span class="material-icons mr-1 text-lg">add</span>
              Add Schedule
            </button>
          </div>
        </div>
        
        <!-- Day Selector -->
        <div class="flex gap-1 overflow-x-auto pb-2">
          <div *ngFor="let day of dayOptions">
            <button 
              (click)="selectDay(day.value)"
              class="px-3 py-1.5 text-sm rounded-md whitespace-nowrap"
              [class]="currentDay === day.value ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
            >
              {{ day.label }}
            </button>
          </div>
        </div>
      </div>
      
      <div class="flex h-full">
        <!-- Left Side Component -->
        <div class="w-64 border-r border-gray-200 bg-gray-50 p-4 overflow-y-auto">
          <h3 class="font-medium text-gray-900 mb-3">Schedule Information</h3>
          <div class="space-y-3">
            <div class="bg-white rounded-lg p-3 shadow-sm">
              <div class="text-sm font-medium text-gray-900">Today's Date</div>
              <div class="text-xs text-gray-500 mt-1">{{ getCurrentDate() }}</div>
            </div>
            
            <div class="bg-white rounded-lg p-3 shadow-sm">
              <div class="text-sm font-medium text-gray-900">Total Schedules</div>
              <div class="text-lg font-semibold text-blue-600 mt-1">{{ schedules ? schedules.length : 0 }}</div>
            </div>
            
            <div class="bg-white rounded-lg p-3 shadow-sm">
              <div class="text-sm font-medium text-gray-900">Current Day</div>
              <div class="text-xs text-gray-500 mt-1">{{ getCurrentDayLabel() }}</div>
            </div>
            
            <div class="bg-white rounded-lg p-3 shadow-sm">
              <div class="text-sm font-medium text-gray-900">Active Schedules</div>
              <div class="text-lg font-semibold text-green-600 mt-1">{{ getActiveSchedulesCount() }}</div>
            </div>
          </div>
          
          <div class="mt-6">
            <h3 class="font-medium text-gray-900 mb-3">Quick Actions</h3>
            <div class="space-y-2">
              <button
                (click)="onAddSchedule.emit()"
                class="w-full flex items-center gap-2 px-3 py-2 text-sm text-blue-600 bg-white rounded-lg border border-blue-200 hover:bg-blue-50"
              >
                <span class="material-icons text-base">add</span>
                Add New Schedule
              </button>
              
              <button
                (click)="exportSchedules()"
                class="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-600 bg-white rounded-lg border border-gray-200 hover:bg-gray-50"
              >
                <span class="material-icons text-base">download</span>
                Export Schedules
              </button>
            </div>
          </div>
        </div>
        
        <!-- Calendar Grid -->
        <div class="flex-1 relative bg-white overflow-hidden">
          <div class="relative" style="min-height: 1440px;">
            <!-- Hour slots -->
            <div *ngFor="let hour of hours">
              <div class="absolute left-0 right-0 pr-2" [style.top.px]="hour * 60">
                <div class="absolute left-0 w-12 text-right pr-2 text-xs text-gray-500 font-mono">
                  {{ formatHour(hour) }}
                </div>
                <div class="absolute left-12 right-0 h-px bg-gray-100"></div>
              </div>
            </div>
            
            <!-- Current time indicator -->
            <div 
              class="absolute left-12 right-0 h-0.5 bg-red-500 z-20"
              [style.top.px]="currentTimePosition"
              *ngIf="currentTimePosition >= 0"
            >
              <div class="absolute -left-2.5 -top-2 w-2.5 h-2.5 bg-red-500 rounded-full"></div>
            </div>
            
            <!-- Events for selected day -->
            <div *ngFor="let event of getEventsForSelectedDay(); trackBy: trackByEventId">
              <div
                class="absolute left-14 right-2 rounded-md p-2 overflow-hidden cursor-pointer transition-all hover:scale-[1.02] hover:shadow-md z-10"
                [style.top.px]="getEventTopPosition(event.startTime)"
                [style.height.px]="getEventHeight(event.startTime, event.endTime)"
                [style.backgroundColor]="getEventColor(event.priority) + '20'"
                [style.borderLeftColor]="getEventColor(event.priority)"
                [style.borderLeftWidth]="'4px'"
              >
                <div class="flex justify-between items-start h-full">
                  <div class="flex-1 min-w-0">
                    <div class="font-medium text-sm truncate">{{ event.title }}</div>
                    <div class="text-xs opacity-75 mt-1">
                      {{ formatTime(event.startTime) }} - {{ formatTime(event.endTime) }}
                    </div>
                    <div class="text-xs opacity-60 mt-1">
                      Priority {{ event.priority }}
                    </div>
                  </div>
                  <div class="flex space-x-1 ml-2">
                    <button
                      (click)="onDeleteSchedule.emit(event.schedule); $event.stopPropagation()"
                      class="p-1 rounded hover:bg-black hover:bg-opacity-10"
                    >
                      <span class="material-icons text-xs">delete</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
    }
  `]
})
export class ScheduleCardComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  @Input() schedules: PlaylistScheduleBase[] = [];
  @Output() onAddSchedule = new EventEmitter<void>();
  @Output() onDeleteSchedule = new EventEmitter<PlaylistScheduleBase>();
  @Output() onExportSchedules = new EventEmitter<void>();

  playlistNames: { [key: string]: string } = {};
  loading = false;
  calendarEvents: CalendarEvent[] = [];
  
  // Days: 0 = Sunday, 1 = Monday, etc.
  currentDay = new Date().getDay();
  dayOptions = [
    { label: 'Sun', value: 0 },
    { label: 'Mon', value: 1 },
    { label: 'Tue', value: 2 },
    { label: 'Wed', value: 3 },
    { label: 'Thu', value: 4 },
    { label: 'Fri', value: 5 },
    { label: 'Sat', value: 6 }
  ];
  
  hours = Array.from({ length: 24 }, (_, i) => i);
  currentTimePosition = -1;
  private timeInterval: any;

  constructor(private playlistService: PlaylistService) {}

  ngOnInit() {
    this.loadPlaylistNames();
    this.updateCurrentTimePosition();

    // Update current time every 30 seconds for more accurate display
    this.timeInterval = setInterval(() => {
      this.updateCurrentTimePosition();
    }, 30000);
  }

  ngAfterViewInit() {
    // No longer needed since we removed scrolling
  }

  ngOnDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['schedules'] && changes['schedules'].currentValue) {
      this.loadPlaylistNames();
      this.generateCalendarEvents();
    }
  }

  private loadPlaylistNames() {
    if (!this.schedules || this.schedules.length === 0) return;

    this.loading = true;
    // Get unique playlist IDs without using Set
    const playlistIds: string[] = [];
    this.schedules.forEach(s => {
      if (!playlistIds.includes(s.playlist_id)) {
        playlistIds.push(s.playlist_id);
      }
    });
    let loadedCount = 0;

    playlistIds.forEach(id => {
      if (!this.playlistNames[id]) {
        this.playlistService.getPlaylist(id).subscribe({
          next: (playlist) => {
            this.playlistNames[id] = playlist ? playlist.name : 'Unknown Playlist';
            this.generateCalendarEvents(); // Regenerate events when playlist names load
          },
          error: (error) => {
            console.error(`Error loading playlist ${id}:`, error);
            this.playlistNames[id] = 'Unknown Playlist';
          },
          complete: () => {
            loadedCount++;
            if (loadedCount === playlistIds.length) {
              this.loading = false;
            }
          }
        });
      } else {
        loadedCount++;
        if (loadedCount === playlistIds.length) {
          this.loading = false;
        }
      }
    });
  }

  private generateCalendarEvents() {
    this.calendarEvents = [];
    
    if (!this.schedules) return;
    
    this.schedules.forEach(schedule => {
      const event: CalendarEvent = {
        id: `${schedule.playlist_id}-${schedule.start_time}-${schedule.end_time}`,
        title: this.playlistNames[schedule.playlist_id] || 'Unknown Playlist',
        startTime: schedule.start_time,
        endTime: schedule.end_time,
        days: schedule.days_of_week || [],
        priority: schedule.priority,
        schedule: schedule
      };
      
      this.calendarEvents.push(event);
    });
  }

  selectDay(day: number) {
    this.currentDay = day;
  }

  getEventsForSelectedDay(): CalendarEvent[] {
    const dayName = this.dayOptions.find(d => d.value === this.currentDay)?.label;
    if (!dayName) return [];
    
    // Convert short day name to full day name
    const fullDayName = this.getFullDayName(dayName);
    return this.calendarEvents.filter(event => 
      event.days && event.days.includes(fullDayName)
    );
  }

  getFullDayName(shortName: string): string {
    const dayMap: { [key: string]: string } = {
      'Sun': 'Sunday',
      'Mon': 'Monday',
      'Tue': 'Tuesday',
      'Wed': 'Wednesday',
      'Thu': 'Thursday',
      'Fri': 'Friday',
      'Sat': 'Saturday'
    };
    return dayMap[shortName] || shortName;
  }

  getEventTopPosition(startTime: string): number {
    if (!startTime) return 0;
    const parts = startTime.split(':');
    if (parts.length < 2) return 0;
    
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    
    if (isNaN(hours) || isNaN(minutes)) return 0;
    
    const totalMinutes = hours * 60 + minutes;
    return totalMinutes;
  }

  getEventHeight(startTime: string, endTime: string): number {
    if (!startTime || !endTime) return 0;
    
    const startParts = startTime.split(':');
    const endParts = endTime.split(':');
    
    if (startParts.length < 2 || endParts.length < 2) return 0;
    
    const startHours = parseInt(startParts[0], 10);
    const startMinutes = parseInt(startParts[1], 10);
    const endHours = parseInt(endParts[0], 10);
    const endMinutes = parseInt(endParts[1], 10);
    
    if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) return 0;
    
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;
    
    // Handle overnight events
    const durationMinutes = endTotalMinutes > startTotalMinutes 
      ? endTotalMinutes - startTotalMinutes 
      : (24 * 60 - startTotalMinutes) + endTotalMinutes;
      
    return Math.max(durationMinutes, 20); // Minimum height of 20px
  }

  formatTime(time: string): string {
    if (!time) return '';
    try {
      const parts = time.split(':');
      if (parts.length < 2) return time;

      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);

      if (isNaN(hours) || isNaN(minutes)) return time;

      const date = new Date();
      date.setHours(hours, minutes);
      return date.toLocaleTimeString([], {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return time;
    }
  }

  formatHour(hour: number): string {
    const date = new Date();
    date.setHours(hour, 0);
    return date.toLocaleTimeString([], {
      hour: 'numeric',
      hour12: true
    });
  }

  getEventColor(priority: number): string {
    switch (priority) {
      case 1: return '#3b82f6'; // blue-500
      case 2: return '#10b981'; // green-500
      case 3: return '#f59e0b'; // amber-500
      default: return '#6b7280'; // gray-500
    }
  }

  updateCurrentTimePosition() {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();

    // Calculate total minutes including seconds for more precise positioning
    const totalMinutes = hours * 60 + minutes + (seconds / 60);
    this.currentTimePosition = Math.min(totalMinutes, 1439); // Cap at 23:59
  }

  getCurrentDate(): string {
    return new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getActiveSchedulesCount(): number {
    if (!this.schedules) return 0;
    // Count schedules that are active today
    const todayEvents = this.getEventsForSelectedDay();
    return todayEvents.length;
  }

  exportSchedules() {
    // Emit event for parent component to handle export
    this.onExportSchedules.emit();
  }

  getCurrentDayLabel(): string {
    const day = this.dayOptions.find(d => d.value === this.currentDay);
    return day ? day.label : '';
  }

  trackByEventId(index: number, event: CalendarEvent): string {
    return event.id;
  }
}