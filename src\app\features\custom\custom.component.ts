import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener, HostBinding } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ScreenService } from '../screens/services/screen.service';
import { Screen, PlaylistScheduleBase, PlaylistSchedule } from '../../models/screen.model';
import { Observable, Subject, takeUntil, map, interval, combineLatest } from 'rxjs';
import { AreaFacade } from '../../core/state/area-state/area.facade';
import { Area } from '../../models/area.model';
import { SupabaseMediaService } from '../../core/services/supabase-media.service';
import { Media } from '../../models/media.model';
import { PlaylistService } from '../playlists/services/playlist.service';
import { Playlist, PlaylistItem as PlaylistItemType } from '../../models/playlist.model';
import { HoverPreviewModalComponent } from './components/hover-preview-modal/hover-preview-modal.component';
import { DragDropModule, CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ScheduleCalendarComponent } from '../../shared/components/schedule-calendar/schedule-calendar.component';
import { ScreenListComponent } from './components/screen-list/screen-list.component';
import { AddScheduleFormComponent } from '../screens/components/screen-details/add-schedule-form/add-schedule-form.component';
import { convertScreenSchedulesToCalendarFormat, convertCalendarScheduleToScreenFormat } from '../../shared/utils/schedule.utils';
import { MediaLibraryComponent } from '../../shared/components/media-library/media-library.component';
import { PlaylistPanelComponent } from '../../shared/components/playlist-panel/playlist-panel.component';

export interface PlaylistItem {
  id: string;
  name: string;
  url: string;
  type: string;
  duration: number;
  color?: string;
}

// Import the Schedule interface from the calendar component
import { Schedule } from '../../shared/components/schedule-calendar/schedule-calendar.component';

// Add interface for tracking selected screen
interface SelectedScreen {
  screenId: string;
  areaId: string;
}

// Add interface for media preview
interface MediaPreview {
  show: boolean;
  item: any; // We'll use any for simplicity, but this should be a proper playlist item
}

// Add interface for hover preview
interface HoverPreview {
  show: boolean;
  item: any;
  mouseX: number;
  mouseY: number;
}

// Add interface for color picker modal
interface ColorPickerModal {
  show: boolean;
  playlist: Playlist | null;
  selectedColor: string;
}

@Component({
  selector: 'app-custom',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, HoverPreviewModalComponent, DragDropModule, ScheduleCalendarComponent, ScreenListComponent, AddScheduleFormComponent, MediaLibraryComponent, PlaylistPanelComponent],
  templateUrl: './custom.component.html',
  styleUrls: ['./custom.component.scss']
})
export class CustomComponent implements OnInit, OnDestroy {
  @HostBinding('class.fullscreen')
  get fullscreenClass(): boolean {
    return this.isFullscreen;
  }

  title = 'Screen Locations';
  description = 'All screen locations displayed automatically';
  screens$: Observable<Screen[]>;
  areas: Area[] = [];
  media$: Observable<Media[]>;
  mediaTypes$: Observable<string[]>;
  isFullscreen = false;
  isMediaPanelExpanded = false; // Default to expanded
  showScreenList = true; // Toggle between complex view and screen list view

  // Track selected screen and area
  selectedScreen: SelectedScreen | null = null;
  selectedArea: Area | null = null;

  // Define a set of distinct colors for playlist items
  private playlistColors = [
    '#dbeafe', // light blue
    '#dcfce7', // light green
    '#f3e8ff', // light purple
    '#ffe4e6', // light red
    '#fef3c7', // light yellow
    '#e0f2fe', // light cyan
    '#ede9fe', // light indigo
    '#fef7cd', // light lime
    '#fce7f3', // light pink
    '#f0f9ff'  // light sky
  ];

  // Playlist properties
  playlist: PlaylistItem[] = [];
  playlists: Playlist[] = []; // Store actual playlist objects
  filteredPlaylists: Playlist[] = []; // For displaying playlists like in the playlists component
  expandedPlaylistItemId: string | null = null; // Track which playlist item is expanded
  expandedPlaylistId: string | null = null; // Track which available playlist is expanded

  // Media preview properties
  mediaPreview: MediaPreview = {
    show: false,
    item: null
  };

  // Hover preview properties
  hoverPreview: HoverPreview = {
    show: false,
    item: null,
    mouseX: 0,
    mouseY: 0
  };
  hoverTimeout: any = null;

  // Color picker properties
  colorPickerModal: ColorPickerModal = {
    show: false,
    playlist: null,
    selectedColor: '#3B82F6'
  };

  // Predefined colors for playlists
  predefinedColors = [
    '#3B82F6', // Blue
    '#10B981', // Emerald
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#8B5CF6', // Violet
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#F97316', // Orange
    '#EC4899', // Pink
    '#6B7280', // Gray
    '#14B8A6', // Teal
    '#A855F7', // Purple
    '#22C55E', // Green
    '#F43F5E', // Rose
    '#0EA5E9', // Sky
    '#D946EF'  // Fuchsia
  ];

  // Schedule properties
  schedules: Schedule[] = [];
  playlistNames: { [key: string]: string } = {};

  currentTime = new Date();
  private destroy$ = new Subject<void>();
  private heartbeatInterval: any;

  constructor(
    private screenService: ScreenService,
    private areaFacade: AreaFacade,
    private router: Router,
    private supabaseMediaService: SupabaseMediaService,
    private playlistService: PlaylistService
  ) {
    this.screens$ = this.screenService.getScreens();
    this.media$ = this.supabaseMediaService.getMedia();
    this.mediaTypes$ = this.media$.pipe(
      map(mediaItems => {
        console.log('Media items loaded:', mediaItems);
        return mediaItems.map(item => item.type);
      }),
      map(types => {
        const uniqueTypes = [...new Set(types)];
        console.log('Unique media types:', uniqueTypes);
        return uniqueTypes;
      })
    );
  }

  ngOnInit(): void {
    this.isFullscreen = this.router.url.includes('/fullscreen');
    if (this.isFullscreen) {
      this.enterFullscreen();
    }
    document.addEventListener('fullscreenchange', this.onFullscreenChange);

    // Load real areas data
    this.areaFacade.loadAreas();
    this.areaFacade.areas$
      .pipe(takeUntil(this.destroy$))
      .subscribe(areas => {
        console.log('Areas loaded:', areas);
        this.areas = areas;
        // Check heartbeats immediately after loading areas
        this.checkHeartbeats();
      });

    // Check heartbeat every 30 seconds
    this.heartbeatInterval = setInterval(() => {
      this.checkHeartbeats();
    }, 30000);

    // Load real playlist data from Supabase
    this.updatePlaylistItems();

    // Load real schedule data from screens (only if no screen is selected)
    this.screenService.getScreens().subscribe(screens => {
      // Only load all schedules if no specific screen is selected
      if (!this.selectedScreen) {
        // Clear existing schedules to avoid duplicates
        this.schedules = [];
        this.playlistNames = {};

        // Extract schedule data from screens
        const allSchedules: PlaylistScheduleBase[] = [];
        
        screens.forEach(screen => {
          if (screen.schedule) {
            const scheduleData = screen.schedule;

            // Add upcoming schedules
            scheduleData.upcoming.forEach(upcoming => {
              allSchedules.push(upcoming);
              
              // Store playlist names
              if (!this.playlistNames[upcoming.playlist_id]) {
                const playlist = this.playlists.find(p => p.id === upcoming.playlist_id);
                this.playlistNames[upcoming.playlist_id] = playlist ? playlist.name : 'Unknown Playlist';
              }
            });
          }
        });

        // Convert to calendar format only after ensuring playlist names are loaded
        // Load any missing playlist names first
        const playlistIds = [...new Set(allSchedules.map(s => s.playlist_id))];
        let loadedCount = 0;
        const totalPlaylists = playlistIds.length;

        if (totalPlaylists === 0) {
          // If no playlists, update schedules immediately
          this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames);
          this.updatePlaylistDisplay();
        } else {
          playlistIds.forEach(id => {
            // If we don't have the playlist name yet, load it
            if (!this.playlistNames[id] || this.playlistNames[id] === 'Unknown Playlist') {
              this.playlistService.getPlaylist(id).subscribe({
                next: (playlist) => {
                  this.playlistNames[id] = playlist ? playlist.name : `Unknown Playlist (${id})`;
                },
                error: (error) => {
                  console.error(`Error loading playlist ${id}:`, error);
                  this.playlistNames[id] = `Unknown Playlist (${id})`;
                },
                complete: () => {
                  loadedCount++;
                  // When all playlists are loaded, update the schedules
                  if (loadedCount === totalPlaylists) {
                    this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames);
                    this.updatePlaylistDisplay();
                  }
                }
              });
            } else {
              loadedCount++;
              // When all playlists are loaded, update the schedules
              if (loadedCount === totalPlaylists) {
                this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames);
                this.updatePlaylistDisplay();
              }
            }
          });
        }
      }
    });

    // Update current time every second
    interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.currentTime = new Date();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    document.removeEventListener('fullscreenchange', this.onFullscreenChange);

    // Clear heartbeat interval
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Clear hover timeout
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }
  }

  checkHeartbeats(): void {
    const now = new Date();
    const updatedAreas = this.areas.map(area => {
      const updatedScreens = area.screens.map(screen => {
        // If last_ping is more than 2 minutes ago, mark as offline
        if (screen.last_ping) {
          const lastPing = new Date(screen.last_ping);
          const diffMinutes = (now.getTime() - lastPing.getTime()) / (1000 * 60);

          // If more than 2 minutes since last ping, mark as offline
          if (diffMinutes > 2) {
            return { ...screen, status: 'offline' as const };
          }
        } else {
          // If no last_ping, mark as offline
          return { ...screen, status: 'offline' as const };
        }

        return screen;
      });

      return { ...area, screens: updatedScreens };
    });

    this.areas = updatedAreas;
  }

  @HostListener('document:keydown.escape', ['$event'])
  onKeydownHandler(event: KeyboardEvent) {
    if (this.isFullscreen) {
      this.exitFullscreen();
    }
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (this.hoverPreview.show) {
      this.hoverPreview.mouseX = event.clientX;
      this.hoverPreview.mouseY = event.clientY;
    }
  }

  private onFullscreenChange = () => {
    if (!document.fullscreenElement) {
      this.isFullscreen = false;
      this.router.navigate(['/custom']);
    }
  }

  toggleFullscreen(): void {
    if (this.isFullscreen) {
      this.exitFullscreen();
    } else {
      this.router.navigate(['/custom/fullscreen']);
    }
  }

  private async enterFullscreen() {
    try {
      const element = document.documentElement;
      if (element.requestFullscreen) {
        await element.requestFullscreen();
        this.isFullscreen = true;
      } else if ((element as any).webkitRequestFullscreen) { /* Safari */
        await (element as any).webkitRequestFullscreen();
        this.isFullscreen = true;
      } else if ((element as any).msRequestFullscreen) { /* IE11 */
        await (element as any).msRequestFullscreen();
        this.isFullscreen = true;
      }
    } catch (err) {
      console.error('Error attempting to enable fullscreen:', err);
    }
  }

  async exitFullscreen(): Promise<void> {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) { /* Safari */
        await (document as any).webkitExitFullscreen();
      } else if ((document as any).msExitFullscreen) { /* IE11 */
        await (document as any).msExitFullscreen();
      }
    } catch (err) {
      console.error('Error attempting to exit fullscreen:', err);
    } finally {
      this.isFullscreen = false;
      this.router.navigate(['/custom']);
    }
  }

  getScreenLocation(screen: Screen): string {
    const location = screen.location;
    // Return only the area information
    return location.area ? location.area : 'No area specified';
  }

  getEffectiveScreenStatus(screen: Screen): string {
    // Use the status from the screen object which is updated by checkHeartbeats
    return screen.status;
  }

  // Get areas for a specific location
  getAreasForLocation(location: string): Area[] {
    return this.areas.filter(area => area.location === location);
  }

  // Get unique locations from areas
  getLocations(): string[] {
    const locations = this.areas
      .filter(area => area.location && area.location.trim() !== '')
      .map(area => area.location);
    return [...new Set(locations)];
  }

  getScreenStatusClass(status: string): string {
    if (this.isFullscreen) {
      switch (status) {
        case 'online':
          return 'bg-green-900 text-green-300';
        case 'offline':
          return 'bg-red-900 text-red-300';
        case 'maintenance':
          return 'bg-yellow-900 text-yellow-300';
        case 'error':
          return 'bg-red-900 text-red-300';
        default:
          return 'bg-gray-700 text-gray-300';
      }
    } else {
      switch (status) {
        case 'online':
          return 'status-online';
        case 'offline':
          return 'status-offline';
        case 'maintenance':
          return 'status-maintenance';
        case 'error':
          return 'status-error';
        default:
          return 'status-unknown';
      }
    }
  }

  // Handle screen click\n  onScreenClick(screen: Screen): void {\n    console.log('Screen clicked:', screen);\n    // Navigate to screen details page or show a modal with screen information\n    // For now, we'll just log the screen data\n    alert(`Screen: ${screen.name}\\nStatus: ${screen.status}\\nLocation: ${this.getScreenLocation(screen)}`);\n  }\n\n  // Update playlist items with actual data from playlists
  private updatePlaylistItems(): void {
    // Subscribe to playlist updates
    this.playlistService.getPlaylists().pipe(
      takeUntil(this.destroy$)
    ).subscribe(playlists => {
      console.log('Playlists updated:', playlists);
      this.playlists = playlists;

      // Also update the playlist display
      this.updatePlaylistDisplay();
    });
  }

  // Update the playlist display based on screens and playlists
  private updatePlaylistDisplay(): void {
    // Always show all playlists in the custom component
    this.filteredPlaylists = [...this.playlists];
    this.updateAllScreensPlaylist();
  }

  // Update playlists for all screens (when no specific screen is selected)
  private updateAllScreensPlaylist(): void {
    // Clear existing playlist items
    this.playlist = [];

    // Get all screens and their playlists
    this.screens$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(screens => {
      screens.forEach(screen => {
        if (screen.current_playlist) {
          // Find the actual playlist
          const playlist = this.playlists.find(p => p.id === screen.current_playlist);
          if (playlist) {
            // Check if playlist item already exists to avoid duplicates
            const exists = this.playlist.some(item => item.id === playlist.id);
            if (!exists) {
              this.playlist.push({
                id: playlist.id,
                name: playlist.name,
                url: '', // Playlists don't have a single URL
                type: 'playlist',
                duration: playlist.duration,
                color: this.playlistColors[this.playlist.length % this.playlistColors.length]
              });
            }
          } else {
            // Fallback to basic playlist info if not found
            const exists = this.playlist.some(item => item.id === screen.current_playlist);
            if (!exists) {
              this.playlist.push({
                id: screen.current_playlist!,
                name: `Playlist for ${screen.name}`,
                url: '',
                type: 'playlist',
                duration: 0,
                color: this.playlistColors[this.playlist.length % this.playlistColors.length]
              });
            }
          }
        }
      });
    });
  }



  // Update data for a specific area
  private updateDataForArea(area: Area): void {
    // Clear existing schedules
    this.schedules = [];
    this.playlistNames = {};

    // Get schedule data for all screens in this area
    const allSchedules: PlaylistScheduleBase[] = [];
    
    area.screens.forEach(screen => {
      if (screen.schedule) {
        const scheduleData = screen.schedule;

        // Add upcoming schedules
        scheduleData.upcoming.forEach(upcoming => {
          allSchedules.push(upcoming);
          
          // Store playlist names
          if (!this.playlistNames[upcoming.playlist_id]) {
            const playlist = this.playlists.find(p => p.id === upcoming.playlist_id);
            this.playlistNames[upcoming.playlist_id] = playlist ? playlist.name : 'Unknown Playlist';
          }
        });
      }
    });

    // Create playlist color map
    const playlistColorMap: { [key: string]: string } = {};
    this.playlists.forEach(playlist => {
      if (playlist.color) {
        playlistColorMap[playlist.id] = playlist.color;
      }
    });

    // Convert to calendar format
    this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames, playlistColorMap);

    // Filter playlists to show only those used by screens in this area
    const areaPlaylistIds = new Set<string>();
    area.screens.forEach(screen => {
      if (screen.current_playlist) {
        areaPlaylistIds.add(screen.current_playlist);
      }
      if (screen.schedule) {
        if (screen.schedule.current?.playlist_id) {
          areaPlaylistIds.add(screen.schedule.current.playlist_id);
        }
        screen.schedule.upcoming.forEach(upcoming => {
          if (upcoming.playlist_id) {
            areaPlaylistIds.add(upcoming.playlist_id);
          }
        });
      }
    });

    // Filter playlists to show only relevant ones for this area
    // Commented out to always show all playlists
    // this.filteredPlaylists = this.playlists.filter(playlist =>
    //   areaPlaylistIds.has(playlist.id)
    // );

    console.log(`Updated data for area: ${area.name}, showing ${this.schedules.length} schedules and ${this.filteredPlaylists.length} playlists`);
  }

  // Update schedules and playlists for a specific screen
  private updateSchedulesForScreen(screen: Screen): void {
    // Clear existing schedules
    this.schedules = [];
    this.playlistNames = {};

    // Get schedule data for this specific screen
    if (screen.schedule) {
      const scheduleData = screen.schedule;

      // Add upcoming schedules
      const allSchedules: PlaylistScheduleBase[] = [...scheduleData.upcoming];

      // Load playlist names for the schedule calendar
      const playlistIds = [...new Set(allSchedules.map(s => s.playlist_id))];
      let loadedCount = 0;
      const totalPlaylists = playlistIds.length;

      if (totalPlaylists === 0) {
        // If no playlists, update schedules immediately
        const playlistColorMap: { [key: string]: string } = {};
        this.playlists.forEach(playlist => {
          if (playlist.color) {
            playlistColorMap[playlist.id] = playlist.color;
          }
        });
        this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames, playlistColorMap, screen.id);
      } else {
        playlistIds.forEach(id => {
          // If we don't have the playlist name yet, load it
          if (!this.playlistNames[id] || this.playlistNames[id] === 'Unknown Playlist') {
            this.playlistService.getPlaylist(id).subscribe({
              next: (playlist) => {
                this.playlistNames[id] = playlist ? playlist.name : `Unknown Playlist (${id})`;
              },
              error: (error) => {
                console.error(`Error loading playlist ${id}:`, error);
                this.playlistNames[id] = `Unknown Playlist (${id})`;
              },
              complete: () => {
                loadedCount++;
                // When all playlists are loaded, update the schedules
                if (loadedCount === totalPlaylists) {
                  const playlistColorMap: { [key: string]: string } = {};
                  this.playlists.forEach(playlist => {
                    if (playlist.color) {
                      playlistColorMap[playlist.id] = playlist.color;
                    }
                  });
                  this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames, playlistColorMap, screen.id);
                }
              }
            });
          } else {
            loadedCount++;
            // When all playlists are loaded, update the schedules
            if (loadedCount === totalPlaylists) {
              const playlistColorMap: { [key: string]: string } = {};
              this.playlists.forEach(playlist => {
                if (playlist.color) {
                  playlistColorMap[playlist.id] = playlist.color;
                }
              });
              this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames, playlistColorMap, screen.id);
            }
          }
        });
      }
    }
  }

  // Playlist methods
  addToPlaylist(media: Media): void {
    console.log('Adding media to playlist:', media);

    // Determine color based on media type
    let color = '#f3f4f6'; // default light gray for unknown types
    if (media.type === 'video') {
      color = '#dbeafe'; // light blue for videos
    } else if (media.type === 'image') {
      color = '#dcfce7'; // light green for images
    } else if (media.type === 'template') {
      color = '#f3e8ff'; // light purple for templates
    }

    // If it's a known type, use a unique color from our palette
    if (media.type === 'video' || media.type === 'image' || media.type === 'template') {
      // Cycle through colors to ensure uniqueness
      const colorIndex = this.playlist.length % this.playlistColors.length;
      color = this.playlistColors[colorIndex];
    }

    const playlistItem: PlaylistItem = {
      id: media.id,
      name: media.name,
      url: media.url,
      type: media.type,
      duration: media.duration || 10, // Default to 10 seconds if no duration
      color: color
    };

    this.playlist.push(playlistItem);
    console.log('Playlist updated:', this.playlist);
  }



  // Format duration like in the playlists component
  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  }

  // Get status background color like in the playlists component
  getStatusBackgroundColor(status: string): string {
    switch (status) {
      case 'active': return '#10B981';  // emerald-500
      case 'draft': return '#6B7280';   // gray-500
      case 'archived': return '#F59E0B'; // amber-500
      default: return '#6B7280';        // gray-500
    }
  }

  removeFromPlaylist(index: number): void {
    console.log('Removing item from playlist at index:', index);
    this.playlist.splice(index, 1);
    console.log('Playlist after removal:', this.playlist);
  }

  movePlaylistItem(index: number, direction: 'up' | 'down'): void {
    console.log('Moving playlist item:', { index, direction });
    if (direction === 'up' && index > 0) {
      [this.playlist[index], this.playlist[index - 1]] = [this.playlist[index - 1], this.playlist[index]];
      console.log('Moved item up:', this.playlist);
    } else if (direction === 'down' && index < this.playlist.length - 1) {
      [this.playlist[index], this.playlist[index + 1]] = [this.playlist[index + 1], this.playlist[index]];
      console.log('Moved item down:', this.playlist);
    }
  }

  clearPlaylist(): void {
    console.log('Clearing playlist');
    this.playlist = [];
    console.log('Playlist cleared:', this.playlist);
  }

  savePlaylist(): void {
    console.log('Saving playlist:', this.playlist);
    // Implement save logic here
    // You can save to your backend service here
  }

  getTotalPlaylistDuration(): number {
    return this.playlist.reduce((total, item) => total + item.duration, 0);
  }

  // Schedule methods
  addScheduleEntry(): void {
    const newSchedule: Schedule = {
      id: Date.now().toString(),
      startTime: '09:00',
      endTime: '10:00',
      playlistName: 'New Playlist',
      duration: 60,
      repeat: 'daily',
      status: 'inactive'
    };
    this.schedules.push(newSchedule);
  }

  removeSchedule(index: number): void {
    this.schedules.splice(index, 1);
  }

  removeScheduleFromCalendar(schedule: Schedule): void {
    const idParts = schedule.id.split('___');
    if (idParts.length < 2) {
      console.error('Could not parse schedule ID for deletion:', schedule.id);
      return;
    }

    const screenId = idParts[0];
    const actualScheduleId = idParts.slice(1).join('___');

    this.screenService.getScreen(screenId).subscribe(screen => {
      if (screen && screen.schedule) {
        const updatedUpcoming = screen.schedule.upcoming.filter(upcoming => {
          const upcomingId = `${upcoming.playlist_id}___${upcoming.start_time}___${upcoming.end_time}`;
          return upcomingId !== actualScheduleId;
        });

        // Check if we're deleting the last schedule
        if (updatedUpcoming.length === 0) {
          // If this is the last schedule, clear the current playlist too
          this.screenService.updateScreen(screenId, {
            current_playlist: null,
            current_playlist_started_at: null,
            schedule: {
              current: null,
              upcoming: []
            }
          }).subscribe({
            next: (updatedScreen) => {
              console.log('Last schedule deleted and playlist cleared');
              this.refreshSchedulesData();
            },
            error: (error) => {
              console.error('Error deleting schedule:', error);
            }
          });
        } else {
          // Just update the schedule as normal
          this.screenService.updateScreen(screenId, {
            schedule: {
              current: screen.schedule.current,
              upcoming: updatedUpcoming
            }
          }).subscribe({
            next: (updatedScreen) => {
              console.log('Schedule deleted successfully');
              this.refreshSchedulesData();
            },
            error: (error) => {
              console.error('Error deleting schedule:', error);
            }
          });
        }
      }
    });
  }

  toggleScheduleStatus(index: number): void {
    const schedule = this.schedules[index];
    schedule.status = schedule.status === 'active' ? 'inactive' : 'active';
  }

  getScheduleStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'online':
        return 'bg-green-400';
      case 'offline':
        return 'bg-gray-400';
      case 'maintenance':
        return 'bg-yellow-400';
      case 'error':
        return 'bg-red-400';
      default:
        return 'bg-gray-400';
    }
  }

  getNextSchedule(): Schedule | null {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const activeSchedules = this.schedules
      .filter(schedule => schedule.status === 'active')
      .map(schedule => {
        const [hours, minutes] = schedule.startTime.split(':').map(Number);
        return {
          ...schedule,
          startTimeMinutes: hours * 60 + minutes
        };
      })
      .filter(schedule => schedule.startTimeMinutes > currentTime)
      .sort((a, b) => a.startTimeMinutes - b.startTimeMinutes);

    return activeSchedules.length > 0 ? activeSchedules[0] : null;
  }



  // Toggle media panel
  toggleMediaPanel(): void {
    this.isMediaPanelExpanded = !this.isMediaPanelExpanded;
  }

  // Toggle playlist item expansion
  togglePlaylistItem(itemId: string): void {
    this.expandedPlaylistItemId = this.expandedPlaylistItemId === itemId ? null : itemId;
  }

  // Check if a playlist item is expanded
  isPlaylistItemExpanded(itemId: string): boolean {
    return this.expandedPlaylistItemId === itemId;
  }

  // Toggle available playlist expansion
  toggleAvailablePlaylist(playlistId: string): void {
    this.expandedPlaylistId = this.expandedPlaylistId === playlistId ? null : playlistId;
  }

  // Check if an available playlist is expanded
  isAvailablePlaylistExpanded(playlistId: string): boolean {
    return this.expandedPlaylistId === playlistId;
  }

  // Get expanded playlists as a Set for the playlist panel component
  getExpandedPlaylistsSet(): Set<string> {
    return this.expandedPlaylistId ? new Set([this.expandedPlaylistId]) : new Set();
  }

  onAreaClick(area: Area): void {
    console.log('Area clicked:', area);
    this.showScreenList = true;
    this.selectedScreen = null;
    this.selectedArea = area;

    // Filter schedules and playlists for this specific area
    this.updateDataForArea(area);
  }

  // Handle screen click
  onScreenClick(screen: Screen, areaId: string): void {
    console.log('Screen clicked:', screen);
    // Set this screen as selected
    this.selectedScreen = {
      screenId: screen.id,
      areaId: areaId
    };

    // Fetch fresh data for this specific screen
    this.screenService.getScreen(screen.id).subscribe(freshScreen => {
      // Filter schedules for this specific screen
      this.updateSchedulesForScreen(freshScreen);
    });

    console.log(`Selected screen: ${screen.name}, showing its schedule`);
    this.showScreenList = false;
  }

  // Handle screen click from screen list component
  onScreenListClick(screen: Screen): void {
    console.log('Screen clicked from list:', screen);
    // Find the area that contains this screen
    let areaId = '';
    for (const area of this.areas) {
      if (area.screens.some(s => s.id === screen.id)) {
        areaId = area.id;
        break;
      }
    }

    // Set this screen as selected
    if (areaId) {
      this.selectedScreen = {
        screenId: screen.id,
        areaId: areaId
      };

      // Fetch fresh data for this specific screen
      this.screenService.getScreen(screen.id).subscribe(freshScreen => {
        // Filter schedules for this specific screen
        this.updateSchedulesForScreen(freshScreen);
      });

      console.log(`Selected screen: ${screen.name}, showing its schedule`);
      this.showScreenList = false;
    }
  }

  // Check if a screen is currently selected
  isScreenSelected(screenId: string): boolean {
    return this.selectedScreen !== null && this.selectedScreen.screenId === screenId;
  }

  // Clear screen selection and show all schedules
  clearScreenSelection(): void {
    this.selectedScreen = null;
    this.selectedArea = null;

    // Fetch fresh data from all screens
    this.screenService.getScreens().subscribe(screens => {
      // Clear existing schedules to avoid duplicates
      this.schedules = [];
      this.playlistNames = {};

      // Extract schedule data from all screens
      const allSchedules: PlaylistScheduleBase[] = [];
      
      screens.forEach(screen => {
        if (screen.schedule) {
          const scheduleData = screen.schedule;

          // Add upcoming schedules
          scheduleData.upcoming.forEach(upcoming => {
            allSchedules.push(upcoming);
            
            // Store playlist names
            if (!this.playlistNames[upcoming.playlist_id]) {
              const playlist = this.playlists.find(p => p.id === upcoming.playlist_id);
              this.playlistNames[upcoming.playlist_id] = playlist ? playlist.name : 'Unknown Playlist';
            }
          });
        }
      });

      // Convert to calendar format
      this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames);
    });
    
    // Update playlist display to show all playlists
    this.updatePlaylistDisplay();
  }

  // Get the name of the currently selected screen
  getSelectedScreenName(): string {
    if (!this.selectedScreen) return '';

    for (const area of this.areas) {
      const screen = area.screens.find(s => s.id === this.selectedScreen!.screenId);
      if (screen) {
        return screen.name;
      }
    }
    return 'Unknown Screen';
  }

  // Get the name of the currently selected area
  getSelectedAreaName(): string {
    return this.selectedArea ? this.selectedArea.name : '';
  }

  // Show media preview
  showMediaPreview(item: any): void {
    this.mediaPreview = {
      show: true,
      item: item
    };
  }

  // Hide media preview
  hideMediaPreview(): void {
    this.mediaPreview = {
      show: false,
      item: null
    };
  }

  // Handle hover preview for playlist items
  onPlaylistItemHover(item: any, event: MouseEvent): void {
    this.hoverPreview.mouseX = event.clientX;
    this.hoverPreview.mouseY = event.clientY;

    // Clear any existing timeout
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }

    // Set timeout to show modal after delay
    this.hoverTimeout = setTimeout(() => {
      this.hoverPreview = {
        show: true,
        item: item,
        mouseX: event.clientX,
        mouseY: event.clientY
      };
    }, 300); // 300ms delay before showing modal
  }

  onPlaylistItemLeave(): void {
    // Clear timeout if mouse leaves before modal appears
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
      this.hoverTimeout = null;
    }

    // Immediately hide modal when leaving the playlist item
    this.hoverPreview.show = false;
  }

  // Get modal position with boundary checking
  getModalLeft(): number {
    // Modal width is max 500px (max-w-lg) + some padding
    const modalWidth = 500;
    const windowWidth = window.innerWidth;

    // Position to the right of mouse, but ensure it stays within viewport
    let left = this.hoverPreview.mouseX + 20;
    if (left + modalWidth > windowWidth) {
      // If it would go off the right edge, position to the left of mouse
      left = this.hoverPreview.mouseX - modalWidth - 20;
    }

    // Ensure it doesn't go off the left edge either
    return Math.max(10, left);
  }

  getModalTop(): number {
    // Modal height is approximately 300px + padding
    const modalHeight = 300;
    const windowHeight = window.innerHeight;

    // Position above mouse, but ensure it stays within viewport
    let top = this.hoverPreview.mouseY - modalHeight - 20;
    if (top < 10) {
      // If it would go off the top edge, position below mouse
      top = this.hoverPreview.mouseY + 20;
    }

    // Ensure it doesn't go off the bottom edge either
    return Math.min(windowHeight - modalHeight - 10, top);
  }

  // Drag and drop methods for media items
  onMediaDragStart(event: DragEvent, media: Media): void {
    console.log('Drag started for media:', media);

    // Set the drag effect
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'copy';

      // Store the media data as JSON in the drag event
      event.dataTransfer.setData('text/plain', JSON.stringify(media));

      // Set a custom drag image
      const dragImage = document.createElement('div');
      dragImage.className = 'drag-image';
      dragImage.innerHTML = `
        <div class="flex items-center">
          <span class="material-icons drag-image-icon mr-2">image</span>
          <span class="truncate max-w-xs">${media.name}</span>
        </div>
      `;
      dragImage.style.position = 'absolute';
      dragImage.style.top = '-1000px'; // Move off-screen
      dragImage.style.backgroundColor = '#3b82f6'; // blue-500
      dragImage.style.color = 'white';
      dragImage.style.padding = '8px 12px';
      dragImage.style.borderRadius = '0.5rem';
      dragImage.style.fontSize = '0.875rem';
      dragImage.style.fontWeight = '500';
      dragImage.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
      dragImage.style.pointerEvents = 'none';
      dragImage.style.zIndex = '9999';
      dragImage.style.maxWidth = '200px';
      dragImage.style.display = 'flex';
      dragImage.style.alignItems = 'center';
      dragImage.style.justifyContent = 'center';
      document.body.appendChild(dragImage);
      event.dataTransfer.setDragImage(dragImage, 0, 0);

      // Add visual feedback to the dragged element
      const target = event.target as HTMLElement;
      if (target) {
        target.classList.add('media-item-dragging');
      }

      // Remove the temporary drag image after a short delay
      setTimeout(() => {
        if (dragImage.parentNode) {
          document.body.removeChild(dragImage);
        }
      }, 0);
    }
  }

  onMediaDragEnd(event: DragEvent): void {
    console.log('Drag ended');
    // Clean up any visual effects if needed
    const target = event.target as HTMLElement;
    if (target) {
      target.classList.remove('media-item-dragging');
    }
  }

  // Drag and drop methods for playlist items
  onPlaylistDragStart(event: DragEvent, playlist: Playlist): void {
    console.log('Drag started for playlist:', playlist);

    // Set the drag effect
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'copy';

      // Store the playlist data as JSON in the drag event
      event.dataTransfer.setData('text/plain', JSON.stringify(playlist));

      // Set a custom drag image
      const dragImage = document.createElement('div');
      dragImage.className = 'drag-image';
      dragImage.innerHTML = `
        <div class="flex items-center">
          <span class="material-icons drag-image-icon mr-2">playlist_play</span>
          <span class="truncate max-w-xs">Schedule: ${playlist.name}</span>
        </div>
      `;
      dragImage.style.position = 'absolute';
      dragImage.style.top = '-1000px'; // Move off-screen
      dragImage.style.backgroundColor = playlist.color || '#3B82F6'; // Use playlist color or default blue
      dragImage.style.color = 'white';
      dragImage.style.padding = '8px 12px';
      dragImage.style.borderRadius = '0.5rem';
      dragImage.style.fontSize = '0.875rem';
      dragImage.style.fontWeight = '500';
      dragImage.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
      dragImage.style.pointerEvents = 'none';
      dragImage.style.zIndex = '9999';
      dragImage.style.maxWidth = '200px';
      dragImage.style.display = 'flex';
      dragImage.style.alignItems = 'center';
      dragImage.style.justifyContent = 'center';
      document.body.appendChild(dragImage);
      event.dataTransfer.setDragImage(dragImage, 0, 0);

      // Add visual feedback to the dragged element
      const target = event.target as HTMLElement;
      if (target) {
        target.classList.add('playlist-dragging');
      }

      // Remove the temporary drag image after a short delay
      setTimeout(() => {
        if (dragImage.parentNode) {
          document.body.removeChild(dragImage);
        }
      }, 0);
    }
  }

  onPlaylistDragEnd(event: DragEvent): void {
    console.log('Playlist drag ended');
    // Clean up any visual effects if needed
    const target = event.target as HTMLElement;
    if (target) {
      target.classList.remove('playlist-dragging');
    }
  }

  // Drag and drop reordering for playlist items
  drop(event: CdkDragDrop<PlaylistItem[]>): void {
    console.log('Drag and drop event:', event);
    moveItemInArray(this.playlist, event.previousIndex, event.currentIndex);
  }

  // Drag and drop reordering for items within a specific playlist
  dropPlaylistItem(event: CdkDragDrop<PlaylistItemType[]>, playlist: Playlist): void {
    console.log('Reordering playlist items:', { event, playlist });

    if (event.previousIndex !== event.currentIndex) {
      // Create a copy of the playlist items array
      const items = [...playlist.items];

      // Reorder the items
      moveItemInArray(items, event.previousIndex, event.currentIndex);

      // Update the playlist with reordered items
      const updatedPlaylist: Playlist = {
        ...playlist,
        items: items,
        // Recalculate duration
        duration: items.reduce((total, item) => total + (item.duration || 0), 0)
      };

      // Update the playlist in the service
      this.playlistService.updatePlaylist(playlist.id, updatedPlaylist).subscribe({
        next: (result) => {
          console.log('Playlist updated successfully:', result);

          // Update the local copies
          const playlistIndex = this.playlists.findIndex(p => p.id === playlist.id);
          if (playlistIndex !== -1) {
            this.playlists[playlistIndex] = result;
          }

          const filteredPlaylistIndex = this.filteredPlaylists.findIndex(p => p.id === playlist.id);
          if (filteredPlaylistIndex !== -1) {
            this.filteredPlaylists[filteredPlaylistIndex] = result;
          }
        },
        error: (error) => {
          console.error('Error updating playlist:', error);
          // In a real app, you might want to show an error message to the user
        }
      });
    }
  }

  // Methods for handling drop targets (available playlists)
  onPlaylistDragOver(event: DragEvent, playlistId: string): void {
    console.log('Drag over playlist:', playlistId);
    event.preventDefault();
    event.stopPropagation();

    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'copy';
    }

    // Add visual feedback to the drop target
    const playlistElement = document.querySelector(`[data-playlist-id="${playlistId}"]`);
    if (playlistElement) {
      playlistElement.classList.add('playlist-drop-zone-active');
    }

    // Add visual feedback to the playlist header
    const playlistHeaderElement = playlistElement?.querySelector('.playlist-header-drop-zone');
    if (playlistHeaderElement) {
      playlistHeaderElement.classList.add('playlist-header-drop-zone-active');
    }
  }

  onPlaylistDragLeave(event: DragEvent, playlistId: string): void {
    console.log('Drag leave playlist:', playlistId);
    event.preventDefault();
    event.stopPropagation();

    // Remove visual feedback from the drop target
    const playlistElement = document.querySelector(`[data-playlist-id="${playlistId}"]`);
    if (playlistElement) {
      playlistElement.classList.remove('playlist-drop-zone-active');
    }

    // Remove visual feedback from the playlist header
    const playlistHeaderElement = playlistElement?.querySelector('.playlist-header-drop-zone');
    if (playlistHeaderElement) {
      playlistHeaderElement.classList.remove('playlist-header-drop-zone-active');
    }
  }

  onPlaylistDrop(event: DragEvent, playlistId: string): void {
    console.log('Dropped on playlist:', playlistId);
    event.preventDefault();
    event.stopPropagation();

    // Remove visual feedback from the drop target
    const playlistElement = document.querySelector(`[data-playlist-id="${playlistId}"]`);
    if (playlistElement) {
      playlistElement.classList.remove('playlist-drop-zone-active');
    }

    // Remove visual feedback from the playlist header
    const playlistHeaderElement = playlistElement?.querySelector('.playlist-header-drop-zone');
    if (playlistHeaderElement) {
      playlistHeaderElement.classList.remove('playlist-header-drop-zone-active');
    }

    // Get the media data from the drag event
    if (event.dataTransfer) {
      try {
        const mediaData = event.dataTransfer.getData('text/plain');
        if (mediaData) {
          const media: Media = JSON.parse(mediaData);
          console.log('Dropped media:', media);

          // Add the media to the playlist
          this.addMediaToPlaylist(playlistId, media);
        } else {
          console.warn('No data found in drag event');
        }
      } catch (error) {
        console.error('Error parsing dropped media data:', error);
      }
    }
  }

  // Add media to a specific playlist
  addMediaToPlaylist(playlistId: string, media: Media): void {
    console.log(`Adding media to playlist ${playlistId}:`, media);

    // Validate inputs
    if (!playlistId || !media) {
      console.error('Invalid playlistId or media data');
      return;
    }

    // Find the playlist in the main playlists array
    const playlistIndex = this.playlists.findIndex(p => p.id === playlistId);
    if (playlistIndex !== -1) {
      const playlist = this.playlists[playlistIndex];

      // Validate that the media doesn't already exist in the playlist
      const itemExists = playlist.items.some(item => item.id === media.id);
      if (itemExists) {
        console.warn(`Media ${media.name} already exists in playlist ${playlist.name}`);
        // Optionally show a message to the user
        return;
      }

      // Create a new playlist item from the media
      const playlistItem: PlaylistItemType = {
        schedule: null, // Add the required schedule property
        id: media.id,
        name: media.name,
        type: media.type as 'image' | 'video' | 'webpage' | 'ticker',
        duration: media.duration || 10, // Default to 10 seconds if no duration
        content: {
          url: media.url,
          thumbnail: media.thumbnail_url || undefined
        },
        settings: {
          transition: 'fade',
          transitionDuration: 0.5,
          scaling: 'fit',
          muted: false,
          loop: false
        }
      };

      // Add the item to the playlist
      const updatedPlaylist: Playlist = {
        ...playlist,
        items: [...playlist.items, playlistItem],
        duration: playlist.duration + playlistItem.duration
      };

      // Update the playlist in the database
      this.playlistService.updatePlaylist(playlistId, updatedPlaylist).subscribe({
        next: (result) => {
          console.log('Playlist updated successfully:', result);

          // Update the local copies
          this.playlists[playlistIndex] = result;

          // Find and update in filteredPlaylists if it exists there
          const filteredPlaylistIndex = this.filteredPlaylists.findIndex(p => p.id === playlistId);
          if (filteredPlaylistIndex !== -1) {
            this.filteredPlaylists[filteredPlaylistIndex] = result;
          }

          // Show success message (in a real app, you might want to show a toast notification)
          console.log(`Successfully added ${media.name} to playlist ${playlist.name}`);
        },
        error: (error) => {
          console.error('Error updating playlist:', error);
          // In a real app, you might want to show an error message to the user
        }
      });
    } else {
      console.error(`Playlist with ID ${playlistId} not found`);
    }
  }

  // Color picker methods
  openColorPicker(playlist: Playlist): void {
    this.colorPickerModal = {
      show: true,
      playlist: playlist,
      selectedColor: playlist.color || '#3B82F6'
    };
  }

  closeColorPicker(): void {
    this.colorPickerModal = {
      show: false,
      playlist: null,
      selectedColor: '#3B82F6'
    };
  }

  selectColor(color: string): void {
    this.colorPickerModal.selectedColor = color;
  }

  savePlaylistColor(): void {
    if (this.colorPickerModal.playlist) {
      const playlistId = this.colorPickerModal.playlist.id;
      const newColor = this.colorPickerModal.selectedColor;

      // Update the playlist with the new color
      this.playlistService.updatePlaylist(playlistId, { color: newColor }).subscribe({
        next: (updatedPlaylist) => {
          console.log('Playlist color updated successfully:', updatedPlaylist);

          // Update the local copies
          const playlistIndex = this.playlists.findIndex(p => p.id === playlistId);
          if (playlistIndex !== -1) {
            this.playlists[playlistIndex] = updatedPlaylist;
          }

          const filteredPlaylistIndex = this.filteredPlaylists.findIndex(p => p.id === playlistId);
          if (filteredPlaylistIndex !== -1) {
            this.filteredPlaylists[filteredPlaylistIndex] = updatedPlaylist;
          }

          // Close the color picker
          this.closeColorPicker();
        },
        error: (error) => {
          console.error('Error updating playlist color:', error);
          // In a real app, you might want to show an error message to the user
        }
      });
    }
  }

  // Toggle between screen list view and complex view
  toggleScreenListView(): void {
    this.showScreenList = !this.showScreenList;
  }

  onScheduleUpdated(schedule: Schedule): void {
    const idParts = schedule.id.split('___');
    if (idParts.length < 4) {
      console.error('Could not parse schedule ID for update:', schedule.id);
      return;
    }

    const screenId = idParts[0];
    const actualScheduleId = idParts.slice(1).join('___');

    this.screenService.getScreen(screenId).subscribe(screen => {
      if (screen && screen.schedule) {
        const scheduleIndex = screen.schedule.upcoming.findIndex(upcoming => {
          const upcomingId = `${upcoming.playlist_id}___${upcoming.start_time}___${upcoming.end_time}`;
          return upcomingId === actualScheduleId;
        });

        if (scheduleIndex !== -1) {
          const updatedUpcoming = [...screen.schedule.upcoming];
          const playlistSchedule = convertCalendarScheduleToScreenFormat({
            ...schedule,
            id: actualScheduleId
          });

          updatedUpcoming[scheduleIndex] = {
            ...updatedUpcoming[scheduleIndex],
            start_time: playlistSchedule.start_time,
            end_time: playlistSchedule.end_time,
            days_of_week: playlistSchedule.days_of_week
          };

          this.screenService.updateScreen(screenId, {
            schedule: {
              current: screen.schedule.current,
              upcoming: updatedUpcoming
            }
          }).subscribe(() => {
            console.log('Schedule updated successfully');
            this.refreshSchedulesData();
          });
        } else {
          console.error('Could not find schedule to update:', actualScheduleId);
        }
      }
    });
  }

  // Method to refresh schedules data after an update
  private refreshSchedulesData(): void {
    // If a specific screen is selected, refresh data for that screen
    if (this.selectedScreen) {
      this.screenService.getScreen(this.selectedScreen.screenId).subscribe(screen => {
        if (screen) {
          this.updateSchedulesForScreen(screen);
        }
      });
    } 
    // Otherwise, refresh all schedules data
    else {
      this.screenService.getScreens().subscribe(screens => {
        // Clear existing schedules to avoid duplicates
        this.schedules = [];
        this.playlistNames = {};

        // Extract schedule data from screens
        const allSchedules: PlaylistScheduleBase[] = [];
        
        screens.forEach(screen => {
          if (screen.schedule) {
            const scheduleData = screen.schedule;

            // Add upcoming schedules
            scheduleData.upcoming.forEach(upcoming => {
              allSchedules.push(upcoming);
              
              // Store playlist names
              if (!this.playlistNames[upcoming.playlist_id]) {
                const playlist = this.playlists.find(p => p.id === upcoming.playlist_id);
                this.playlistNames[upcoming.playlist_id] = playlist ? playlist.name : 'Unknown Playlist';
              }
            });
          }
        });

        // Convert to calendar format only after ensuring playlist names are loaded
        // Load any missing playlist names first
        const playlistIds = [...new Set(allSchedules.map(s => s.playlist_id))];
        let loadedCount = 0;
        const totalPlaylists = playlistIds.length;

        if (totalPlaylists === 0) {
          // If no playlists, update schedules immediately
          this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames);
        } else {
          playlistIds.forEach(id => {
            // If we don't have the playlist name yet, load it
            if (!this.playlistNames[id] || this.playlistNames[id] === 'Unknown Playlist') {
              this.playlistService.getPlaylist(id).subscribe({
                next: (playlist) => {
                  this.playlistNames[id] = playlist ? playlist.name : `Unknown Playlist (${id})`;
                },
                error: (error) => {
                  console.error(`Error loading playlist ${id}:`, error);
                  this.playlistNames[id] = `Unknown Playlist (${id})`;
                },
                complete: () => {
                  loadedCount++;
                  // When all playlists are loaded, update the schedules
                  if (loadedCount === totalPlaylists) {
                    this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames);
                  }
                }
              });
            } else {
              loadedCount++;
              // When all playlists are loaded, update the schedules
              if (loadedCount === totalPlaylists) {
                this.schedules = convertScreenSchedulesToCalendarFormat(allSchedules, this.playlistNames);
              }
            }
          });
        }
      });
    }
  }

  // Handle when a playlist is dropped on the calendar
  onPlaylistDropped(event: { playlist: Playlist, timeSlot: { hour: number, minute: number } }): void {
    console.log('Playlist dropped on calendar:', event);
    
    // If no screen is selected, we can't schedule anything
    if (!this.selectedScreen) {
      console.warn('No screen selected, cannot schedule playlist');
      // In a real app, you might want to show a message to the user
      return;
    }
    
    // Create a default schedule for the dropped playlist
    const { playlist, timeSlot } = event;
    
    // Format the time as HH:MM
    const formattedTime = `${timeSlot.hour.toString().padStart(2, '0')}:${timeSlot.minute.toString().padStart(2, '0')}`;
    
    // Create a default end time (1 hour after start time)
    const endHour = (timeSlot.hour + 1) % 24;
    const formattedEndTime = `${endHour.toString().padStart(2, '0')}:${timeSlot.minute.toString().padStart(2, '0')}`;
    
    // Create a new schedule entry
    const newSchedule: PlaylistScheduleBase = {
      playlist_id: playlist.id,
      start_time: formattedTime,
      end_time: formattedEndTime,
      priority: 1,
      days_of_week: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] // Default to all days
    };
    
    // Get the currently selected screen
    this.screenService.getScreen(this.selectedScreen.screenId).subscribe(screen => {
      if (screen) {
        // Get existing schedules or initialize empty array
        const existingSchedules = screen.schedule?.upcoming || [];
        
        // Add the new schedule
        const updatedSchedules = [...existingSchedules, newSchedule];
        
        // Update the screen with the new schedule
        this.screenService.updateScreen(this.selectedScreen!.screenId, {
          schedule: {
            current: screen.schedule?.current || null,
            upcoming: updatedSchedules
          }
        }).subscribe({
          next: () => {
            console.log('Playlist scheduled successfully');
            // Refresh the schedules data to update the calendar
            this.refreshSchedulesData();
            // In a real app, you might want to show a success message to the user
          },
          error: (error) => {
            console.error('Error scheduling playlist:', error);
            // In a real app, you might want to show an error message to the user
          }
        });
      }
    });
  }

  // Remove an item from an available playlist
  removeItemFromAvailablePlaylist(playlistId: string, itemIndex: number): void {
    console.log(`Removing item at index ${itemIndex} from playlist ${playlistId}`);
    
    // Find the playlist in the main playlists array
    const playlistIndex = this.playlists.findIndex(p => p.id === playlistId);
    if (playlistIndex !== -1) {
      const playlist = this.playlists[playlistIndex];
      
      // Check if itemIndex is valid
      if (itemIndex >= 0 && itemIndex < playlist.items.length) {
        // Create a copy of the playlist items array without the item at itemIndex
        const updatedItems = [...playlist.items];
        updatedItems.splice(itemIndex, 1);
        
        // Calculate the new duration
        const newDuration = updatedItems.reduce((total, item) => total + (item.duration || 0), 0);
        
        // Create updated playlist
        const updatedPlaylist: Playlist = {
          ...playlist,
          items: updatedItems,
          duration: newDuration
        };
        
        // Update the playlist in the service
        this.playlistService.updatePlaylist(playlistId, updatedPlaylist).subscribe({
          next: (result) => {
            console.log('Playlist updated successfully:', result);
            
            // Update the local copies
            this.playlists[playlistIndex] = result;
            
            const filteredPlaylistIndex = this.filteredPlaylists.findIndex(p => p.id === playlistId);
            if (filteredPlaylistIndex !== -1) {
              this.filteredPlaylists[filteredPlaylistIndex] = result;
            }
          },
          error: (error) => {
            console.error('Error updating playlist:', error);
            // In a real app, you might want to show an error message to the user
          }
        });
      } else {
        console.error(`Invalid item index ${itemIndex} for playlist ${playlistId}`);
      }
    } else {
      console.error(`Playlist with ID ${playlistId} not found`);
    }
  }
}