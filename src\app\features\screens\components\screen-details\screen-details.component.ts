// Updated screen-details.component.ts with all necessary type fixes

import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { PlaylistSchedule, PlaylistScheduleBase, Screen } from '../../../../models/screen.model';
import { ScreenService } from '../../services/screen.service';
import { SupabaseScreenService } from '../../../../core/services/supabase-screen.service';
import { ScreenStatusHeaderComponent } from './screen-status-header/screen-status-header.component';
import { Schedule, ScheduleCalendarComponent } from '../../../../shared/components/schedule-calendar/schedule-calendar.component';
import { ScreenInfoCardComponent } from './screen-info-card/screen-info-card.component';
import { AddScheduleFormComponent } from './add-schedule-form/add-schedule-form.component';
import { Playlist, PlaylistItem as PlaylistItemType } from '../../../../models/playlist.model';
import { PlaylistService } from '../../../playlists/services/playlist.service';
import { interval, Subject, takeUntil, Observable } from 'rxjs';
import { PlaylistDetailsDialogComponent } from './playlist-details-dialog/playlist-details-dialog.component';
import { PlaylistPreviewDialogComponent } from '../../../playlists/components/playlist-preview-dialog/playlist-preview-dialog.component';
import { CreatePlaylistDialogComponent } from '../../../playlists/components/create-playlist-dialog/create-playlist-dialog.component';
import { TvDisplayComponent } from '../../../../shared/components/tv-display/tv-display.component';
import { supabase } from '../../../../core/services/supabase.config';
import { convertScreenSchedulesToCalendarFormat, convertCalendarScheduleToScreenFormat } from '../../../../shared/utils/schedule.utils';
import { MediaLibraryComponent } from '../../../../shared/components/media-library/media-library.component';
import { PlaylistPanelComponent } from '../../../../shared/components/playlist-panel/playlist-panel.component';
import { SupabaseMediaService } from '../../../../core/services/supabase-media.service';
import { Media } from '../../../../models/media.model';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

@Component({
  selector: 'app-screen-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ScreenStatusHeaderComponent,
    ScheduleCalendarComponent,
    ScreenInfoCardComponent,
    AddScheduleFormComponent,
    PlaylistDetailsDialogComponent,
    PlaylistPreviewDialogComponent,
    CreatePlaylistDialogComponent,
    TvDisplayComponent,
    MediaLibraryComponent,
    PlaylistPanelComponent
  ],
  templateUrl: './screen-details.component.html',
  styleUrls: ['./screen-details.component.scss']
})
export class ScreenDetailsComponent implements OnInit, OnDestroy {
  screen: Screen | null = null;
  loading = true;
  error: string | null = null;
  showAddSchedule = false;
  showPlaylistDetails = false;
  showEditPlaylistDialog = false;
  selectedPlaylist: Playlist | null = null;
  playlist: Playlist | null = null;
  playlistNames: { [key: string]: string } = {};
  showDeleteConfirm = false;
  scheduleToDelete: PlaylistScheduleBase | null = null;
  showDeleteScreenConfirm = false;
  deletingScreen = false;
  currentPlaylistData: any = null;
  isUpdatingOrientation = false;

  schedules: Schedule[] = [];
  private destroy$ = new Subject<void>();
  private refreshInterval$ = interval(10000); // Poll every 10 seconds
  private scheduleChecker: any;

  availablePlaylists: Playlist[] = [];
  media$: Observable<Media[]>;
  filteredPlaylists: Playlist[] = [];
  expandedPlaylistId: string | null = null;
  showCreatePlaylistDialog = false;

  constructor(
    private route: ActivatedRoute,
    private screenService: ScreenService,
    private playlistService: PlaylistService,
    private router: Router,
    private supabaseScreenService: SupabaseScreenService,
    private supabaseMediaService: SupabaseMediaService
  ) {
    this.media$ = this.supabaseMediaService.getMedia();
  }

  ngOnInit(): void {
    this.loadScreen();
    this.setupAutoRefresh();
    this.loadPlaylists(); // Load playlists when component initializes
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.screenService.getScreen(id).subscribe({
        next: (screen) => {
          this.screen = screen;
          // Load current playlist data when screen is loaded
          this.refreshCurrentPlaylistData();
        },
        error: (error) => {
          console.error('Error loading screen:', error);
        }
      });
    }
  }

  loadPlaylists(): void {
    this.playlistService.getPlaylists().subscribe({
      next: (playlists) => {
        this.availablePlaylists = playlists || [];
        console.log('Loaded playlists for screen details:', this.availablePlaylists);
      },
      error: (error) => {
        console.error('Error loading playlists:', error);
        this.availablePlaylists = [];
      }
    });
  }

  onCreatePlaylist(): void {
    this.showCreatePlaylistDialog = true;
  }

  handleCreatePlaylist(): void {
    this.showCreatePlaylistDialog = false;
    this.loadPlaylists(); // Reload playlists to show the newly created one
  }

  ngOnDestroy(): void {
    // Clean up all subscriptions and checkers
    if (this.screen) {
      this.screenService.stopScheduleChecker(this.screen.id);
    }
    this.destroy$.next();
    this.destroy$.complete();
  }

  getSelectedScreenName(): string {
    if (this.screen) {
      return this.screen.name;
    }
    return '';
  }

  retryLoading(): void {
    this.loadScreen();
  }

  private setupAutoRefresh(): void {
    const screenId = this.route.snapshot.paramMap.get('id');
    if (screenId) {
      this.refreshInterval$.pipe(
        takeUntil(this.destroy$)
      ).subscribe(() => {
        this.refreshScreen(screenId);
      });
    }
  }

  private refreshScreen(screenId: string): void {
    this.screenService.getScreen(screenId).subscribe({
      next: (updatedScreen) => {
        this.screen = updatedScreen;
        // Refresh current playlist data when screen is updated
        this.refreshCurrentPlaylistData();
        // Load playlist names for the schedule calendar
        this.loadPlaylistNames();
      },
      error: (error) => {
        console.error('Error refreshing screen:', error);
      }
    });
  }

  loadScreen(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loading = true;
      this.error = null;
      this.screenService.getScreen(id).subscribe({
        next: (screen) => {
          this.screen = screen;
          this.loading = false;
          
          // Load playlist names for the schedule calendar
          this.loadPlaylistNames();
          
          // Load current playlist data if one is assigned
          this.refreshCurrentPlaylistData();
          
          // Start schedule checker if there are upcoming schedules
          if (screen.schedule?.upcoming?.length) {
            this.screenService.startScheduleChecker(screen.id);
            this.screenService.updateCurrentPlaylistFromSchedule(screen.id);
          }
        },
        error: (error) => {
          console.error('Error loading screen:', error);
          this.error = 'Failed to load screen details. Please try again.';
          this.loading = false;
        },
      });
    }
  }

  private loadPlaylistNames(): void {
    if (!this.screen?.schedule?.upcoming || this.screen.schedule.upcoming.length === 0) {
      // Even if there are no schedules, we still need to update the calendar
      this.schedules = this.convertToScheduleFormat(this.screen?.schedule?.upcoming || []);
      return;
    }

    const playlistIds = [...new Set(this.screen.schedule.upcoming.map(s => s.playlist_id))];
    let loadedCount = 0;
    const totalPlaylists = playlistIds.length;

    // If no playlists to load, update schedules immediately
    if (totalPlaylists === 0) {
      this.schedules = this.convertToScheduleFormat(this.screen.schedule.upcoming);
      return;
    }

    playlistIds.forEach(id => {
      if (!this.playlistNames[id]) {
        this.playlistService.getPlaylist(id).subscribe({
          next: (playlist) => {
            this.playlistNames[id] = playlist ? playlist.name : `Unknown Playlist (${id})`;
          },
          error: (error) => {
            console.error(`Error loading playlist ${id}:`, error);
            this.playlistNames[id] = `Unknown Playlist (${id})`;
          },
          complete: () => {
            loadedCount++;
            // When all playlists are loaded, update the schedules
            if (loadedCount === totalPlaylists) {
              this.schedules = this.convertToScheduleFormat(this.screen!.schedule!.upcoming);
            }
          }
        });
      } else {
        loadedCount++;
        // When all playlists are loaded, update the schedules
        if (loadedCount === totalPlaylists) {
          this.schedules = this.convertToScheduleFormat(this.screen!.schedule!.upcoming);
        }
      }
    });
  }

  // Improve handleAddSchedule to update current playlist immediately
handleAddSchedule(schedule: PlaylistSchedule): void {
  if (this.screen) {
    // Create a properly typed schedule object with default empty array
    const currentSchedule = this.screen.schedule || { current: null, upcoming: [] };
    
    const baseSchedule: PlaylistScheduleBase = {
      playlist_id: schedule.playlist_id,
      start_time: schedule.start_time,
      end_time: schedule.end_time,
      priority: schedule.priority,
      days_of_week: schedule.days_of_week  
    };

    // Create a new upcoming array to ensure it's properly typed
    const updatedUpcoming = [...(currentSchedule.upcoming || []), baseSchedule];

    // Check if the new schedule should be activated immediately
    const now = new Date();
    const currentTime = now.toTimeString().split(':').slice(0, 2).join(':');
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' });
    
    // Ensure days_of_week exists before checking
    const scheduleDays = schedule.days_of_week || [];
    const shouldActivateImmediately = 
      scheduleDays.includes(currentDay) &&
      currentTime >= schedule.start_time && 
      currentTime <= schedule.end_time;

    if (shouldActivateImmediately) {
      // If the schedule should start immediately, update the current playlist directly
      this.screenService
        .updateScreen(this.screen.id, {
          current_playlist: schedule.playlist_id,
          current_playlist_started_at: new Date().toISOString(),
          schedule: {
            current: baseSchedule,
            upcoming: updatedUpcoming
          }
        })
        .subscribe({
          next: (updatedScreen) => {
            console.log('Schedule added and playlist activated immediately');
            this.screen = updatedScreen;
            // Refresh current playlist data
            this.refreshCurrentPlaylistData();
            this.showAddSchedule = false;
          },
          error: (error) => {
            console.error('Error adding and activating schedule:', error);
          },
        });
    } else {
      // If the schedule doesn't apply now, just update the upcoming list
      this.screenService
        .updateScreen(this.screen.id, {
          schedule: {
            current: currentSchedule.current,
            upcoming: updatedUpcoming
          }
        })
        .subscribe({
          next: (updatedScreen) => {
            console.log('Schedule added successfully');
            this.screen = updatedScreen;
            // Refresh current playlist data
            this.refreshCurrentPlaylistData();
            this.showAddSchedule = false;

            // Check if the new schedule should be current based on other criteria
            // (like having highest priority among eligible schedules)
            this.screenService.updateCurrentPlaylistFromSchedule(this.screen.id);
          },
          error: (error) => {
            console.error('Error adding schedule:', error);
          },
        });
    }
  }
}

  getPlaylistName(playlistId: string): string {
    this.playlistService.getPlaylist(playlistId).subscribe({
      next: (playlist) => {
        console.log("playlist get name");
        console.log(playlist);
        this.playlist = playlist;
      },
      error: (error) => {
        console.error('Error loading playlist:', error);
      },
    });

    return 'Unknown Playlist';
  }

  getPriorityClass(priority: number): string {
    switch (priority) {
      case 1:
        return 'bg-red-100 text-red-800';
      case 2:
        return 'bg-yellow-100 text-yellow-800';
      case 3:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  formatTime(time: string): string {
    return new Date(`1970-01-01T${time}`).toLocaleTimeString([], {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }

  

  // Improve the deleteSchedule method to ensure current playlist is cleared when all schedules are deleted
deleteSchedule(scheduleToDelete: PlaylistScheduleBase): void {
  if (this.screen?.schedule) {
    // Create a properly typed filtered array of upcoming schedules
    const updatedUpcoming = (this.screen.schedule.upcoming || []).filter(
      schedule => 
        schedule.playlist_id !== scheduleToDelete.playlist_id ||
        schedule.start_time !== scheduleToDelete.start_time
    );

    // Create a properly typed updated schedule object
    const updatedSchedule = {
      current: this.screen.schedule.current,
      upcoming: updatedUpcoming
    };

    // Check if we're deleting the last schedule
    if (updatedUpcoming.length === 0) {
      // If this is the last schedule, clear the current playlist too
      this.screenService.updateScreen(this.screen.id, {
        current_playlist: null,
        current_playlist_started_at: null,
        schedule: {
          current: null,
          upcoming: []
        }
      }).subscribe({
        next: (updatedScreen) => {
            this.screen = updatedScreen;
            // Refresh current playlist data
            this.refreshCurrentPlaylistData();
            console.log('Last schedule deleted and playlist cleared');
          },
        error: (error) => {
          console.error('Error deleting schedule:', error);
        }
      });
    }
    // Check if we're deleting the currently active schedule
    else if (
      this.screen.schedule.current && 
      this.screen.schedule.current.playlist_id === scheduleToDelete.playlist_id && 
      this.screen.schedule.current.start_time === scheduleToDelete.start_time
    ) {
      // If deleting the currently active schedule, clear current and check for new applicable schedule
      this.screenService.updateScreen(this.screen.id, {
        current_playlist: null,
        current_playlist_started_at: null,
        schedule: updatedSchedule
      }).subscribe({
        next: (updatedScreen) => {
          this.screen = updatedScreen;
          // Refresh current playlist data
          this.refreshCurrentPlaylistData();
          console.log('Current schedule deleted and playlist cleared');
          
          // Check if another schedule should now be active
          this.screenService.updateCurrentPlaylistFromSchedule(this.screen.id);
        },
        error: (error) => {
          console.error('Error deleting current schedule:', error);
        }
      });
    }
    else {
      // Just update the schedule as normal
      this.screenService.updateScreen(this.screen.id, {
        schedule: updatedSchedule
      }).subscribe({
        next: (updatedScreen) => {
          this.screen = updatedScreen;
          // Refresh current playlist data
          this.refreshCurrentPlaylistData();
          console.log('Schedule deleted successfully');
        },
        error: (error) => {
          console.error('Error deleting schedule:', error);
        }
      });
    }
  }
}

  viewPlaylist(): void {
    if (this.screen?.current_playlist) {
      console.log('Viewing playlist:', this.screen.current_playlist);
    }
  }

  assignPlaylist(playlist: Playlist) {
    if (this.screen) {
      this.screenService.updateScreen(this.screen.id, {
        current_playlist: playlist.id,
        current_playlist_started_at: new Date().toISOString()
      }).subscribe({
        next: (updatedScreen) => {
          this.screen = updatedScreen;
          // Refresh current playlist data
          this.refreshCurrentPlaylistData();
        },
        error: (error) => {
          console.error('Error assigning playlist:', error);
        }
      });
    }
  }

  handlePlayStatusChange(isPlaying: boolean) {
    if (this.screen) {
      // Here you would typically send a command to your actual screen device
      console.log(`${isPlaying ? 'Playing' : 'Pausing'} playlist on screen ${this.screen.id}`);
    }
  }

  

  closePreviewDialog(): void {
    this.showEditPlaylistDialog = false;
    this.selectedPlaylist = null;
  }

  handlePlaylistAssigned(playlist: Playlist) {
    if (this.screen) {
      this.screenService.updateScreen(this.screen.id, {
        current_playlist: playlist.id,
        current_playlist_started_at: new Date().toISOString()
      }).subscribe({
        next: (updatedScreen) => {
          this.screen = updatedScreen;
          // Refresh current playlist data
          this.refreshCurrentPlaylistData();
          this.showPlaylistDetails = false;
        },
        error: (error) => {
          console.error('Error assigning playlist:', error);
        }
      });
    }
  }

  

  // Add a method to explicitly clear the current playlist
  clearCurrentPlaylist(): void {
    if (this.screen) {
      // Ensure we use a properly typed schedule object
      const updatedSchedule = {
        current: null,
        upcoming: this.screen.schedule?.upcoming || []
      };
      
      this.screenService.updateScreen(this.screen.id, {
        current_playlist: null,
        current_playlist_started_at: null,
        schedule: updatedSchedule
      }).subscribe({
        next: (updatedScreen) => {
          this.screen = updatedScreen;
          // Refresh current playlist data
          this.refreshCurrentPlaylistData();
          console.log('Current playlist cleared');
        },
        error: (error) => {
          console.error('Error clearing current playlist:', error);
        }
      });
    }
  }

  

  // Convert PlaylistScheduleBase[] to Schedule[] for the calendar component
  convertToScheduleFormat(schedules: PlaylistScheduleBase[]): Schedule[] {
    if (!this.screen) return [];

    // Create playlist color map
    const playlistColorMap: { [key: string]: string } = {};
    this.availablePlaylists.forEach((playlist: Playlist) => {
      if (playlist.color) {
        playlistColorMap[playlist.id] = playlist.color;
      }
    });

    const calendarSchedules = convertScreenSchedulesToCalendarFormat(
      schedules,
      this.playlistNames,
      playlistColorMap,
      this.screen.id
    );
    return calendarSchedules;
  }

  removeScheduleFromCalendar(schedule: Schedule): void {
    const idParts = schedule.id.split('___');
    if (idParts.length < 2) {
      console.error('Could not parse schedule ID for deletion:', schedule.id);
      return;
    }

    const screenId = idParts[0];
    const actualScheduleId = idParts.slice(1).join('___');

    if (this.screen && screenId === this.screen.id && this.screen.schedule) {
      const updatedUpcoming = this.screen.schedule.upcoming.filter(upcoming => {
        const upcomingId = `${upcoming.playlist_id}___${upcoming.start_time}___${upcoming.end_time}`;
        return upcomingId !== actualScheduleId;
      });

      // Check if we're deleting the last schedule
      if (updatedUpcoming.length === 0) {
        // If this is the last schedule, clear the current playlist too
        this.screenService.updateScreen(screenId, {
          current_playlist: null,
          current_playlist_started_at: null,
          schedule: {
            current: null,
            upcoming: []
          }
        }).subscribe({
          next: (updatedScreen) => {
            this.screen = updatedScreen;
            // Refresh current playlist data
            this.refreshCurrentPlaylistData();
            console.log('Last schedule deleted and playlist cleared');
            this.refreshSchedulesData();
          },
          error: (error) => {
            console.error('Error deleting schedule:', error);
          }
        });
      } else {
        // Just update the schedule as normal
        this.screenService.updateScreen(screenId, {
          schedule: {
            current: this.screen.schedule.current,
            upcoming: updatedUpcoming
          }
        }).subscribe({
          next: (updatedScreen) => {
            this.screen = updatedScreen;
            // Refresh current playlist data
            this.refreshCurrentPlaylistData();
            console.log('Schedule deleted successfully');
            this.refreshSchedulesData();
          },
          error: (error) => {
            console.error('Error deleting schedule:', error);
          }
        });
      }
    }
  }

  // Add a method to handle adding schedules directly without modal
  handleAddScheduleDirect(): void {
    // Ensure playlists are loaded
    if (this.availablePlaylists.length === 0) {
      this.loadPlaylists();
    }
    
    // Show the add schedule form
    this.showAddSchedule = true;
  }

  // Add a refresh method to the component to handle UI updates
refreshScreenData(): void {
  if (!this.screen) return;
  
  // First, update the current playlist based on schedules
  this.screenService.updateCurrentPlaylistFromSchedule(this.screen.id).then(() => {
    // Then reload the screen data to get the updated information
    this.screenService.getScreen(this.screen!.id).subscribe({
      next: (updatedScreen) => {
        this.screen = updatedScreen;
        // Refresh current playlist data
        this.refreshCurrentPlaylistData();
        console.log('Screen data refreshed with updated playlist information');
      },
      error: (error) => {
        console.error('Error refreshing screen data:', error);
      }
    });
  }).catch(error => {
    console.error('Error updating current playlist:', error);
  });
}

// Update onScheduleAdded method to refresh data
onScheduleAdded(): void {
  this.showAddSchedule = false;
  this.refreshScreenData();
}


// Add this timezone handling method to the supabase-screen.service.ts

// Helper method to get the user's local time format for schedule checks
private getCurrentLocalTime(): { currentTime: string, currentDay: string } {
  const now = new Date();
  
  // Get time in 24-hour format (HH:MM) using the user's local timezone
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const currentTime = `${hours}:${minutes}`;
  
  // Get day of week in English (Monday, Tuesday, etc.) using the user's local timezone
  const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' });
  
  return { currentTime, currentDay };
}

// Update the updateCurrentPlaylistFromSchedule method to use local timezone
async updateCurrentPlaylistFromSchedule(screenId: string): Promise<void> {
  try {
    const { data: screen, error: getError } = await supabase
      .from('screens')
      .select('*')
      .eq('id', screenId)
      .single();

    if (getError) throw getError;
    
    // If there are no upcoming schedules, clear the current playlist
    if (!screen.schedule?.upcoming || screen.schedule.upcoming.length === 0) {
      console.log(`No schedules found for screen ${screenId}, clearing current playlist`);
      
      const { error: clearError } = await supabase
        .from('screens')
        .update({
          current_playlist: null,
          current_playlist_started_at: null,
          schedule: {
            current: null,
            upcoming: []
          }
        })
        .eq('id', screenId);

      if (clearError) throw clearError;
      return;
    }

    // Get the user's local time and day
    const { currentTime, currentDay } = this.getCurrentLocalTime();

    console.log(`Current day: ${currentDay}, current time: ${currentTime} (local timezone)`);

    // Sort schedules by priority first, then start_time
    const sortedSchedules = [...screen.schedule.upcoming].sort((a, b) => {
      // First sort by priority (lower number = higher priority)
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      // Then sort by start_time
      return a.start_time.localeCompare(b.start_time);
    });

    // Find the currently active schedule that matches the current day
    const activeSchedule = sortedSchedules.find(schedule => {
      // Check if schedule has days_of_week and if it should run on current day
      const isValidDay = schedule.days_of_week?.includes(currentDay);
      const isInTimeRange = currentTime >= schedule.start_time && currentTime <= schedule.end_time;
      
      console.log(`Schedule ${schedule.playlist_id}: day valid=${isValidDay}, time valid=${isInTimeRange}`);
      
      return isValidDay && isInTimeRange;
    });

    // If we found an active schedule and it's different from current playlist
    if (activeSchedule && activeSchedule.playlist_id !== screen.current_playlist) {
      console.log(`Activating schedule for playlist ${activeSchedule.playlist_id}`);
      
      const { error: updateError } = await supabase
        .from('screens')
        .update({
          current_playlist: activeSchedule.playlist_id,
          current_playlist_started_at: new Date().toISOString(),
          schedule: {
            ...screen.schedule,
            current: activeSchedule
          }
        })
        .eq('id', screenId);

      if (updateError) throw updateError;
    }
    // If no active schedule found, clear current playlist
    else if (!activeSchedule && screen.current_playlist) {
      console.log('No active schedule found, clearing current playlist');
      
      const { error: clearError } = await supabase
        .from('screens')
        .update({
          current_playlist: null,
          current_playlist_started_at: null,
          schedule: {
            ...screen.schedule,
            current: null
          }
        })
        .eq('id', screenId);

      if (clearError) throw clearError;
    }

  } catch (error) {
    console.error('Error updating current playlist:', error);
    throw error;
  }
}

toggleOrientation(): void {
  if (!this.screen || this.isUpdatingOrientation) return;
  
  this.isUpdatingOrientation = true;
  
  // Toggle to the opposite orientation
  const newOrientation = this.screen.orientation === 'landscape' ? 'portrait' : 'landscape';
  
  this.handleOrientationChange(newOrientation);
}

handleOrientationChange(orientation: 'landscape' | 'portrait'): void {
  if (!this.screen) return;
  
  console.log(`Changing screen orientation to: ${orientation}`);
  
  this.supabaseScreenService.updateScreenOrientation(this.screen.id, orientation).subscribe({
    next: (updatedScreen) => {
      this.screen = updatedScreen;
      // Refresh current playlist data
      this.refreshCurrentPlaylistData();
      console.log('Screen orientation updated successfully');
      
      // Show success feedback
      this.showOrientationSuccess(orientation);
      
      // Reset updating state
      this.isUpdatingOrientation = false;
    },
    error: (error) => {
      console.error('Error updating screen orientation:', error);
      // Show error feedback
      this.showOrientationError();
      
      // Reset updating state
      this.isUpdatingOrientation = false;
    }
  });
}

private showOrientationSuccess(orientation: 'landscape' | 'portrait'): void {
  // You could implement a toast notification here
  console.log(`Successfully switched to ${orientation} mode`);
}

private showOrientationError(): void {
  // You could implement a toast notification here
  console.log('Failed to update screen orientation');
}

confirmDeleteScreen(): void {
  if (!this.screen) return;
  
  this.deletingScreen = true;
  
  this.screenService.deleteScreen(this.screen.id).subscribe({
    next: () => {
      console.log('Screen deleted successfully');
      this.deletingScreen = false;
      this.showDeleteScreenConfirm = false;
      
      // Navigate back to screens list
      this.router.navigate(['/screens']);
    },
    error: (error) => {
      console.error('Error deleting screen:', error);
      this.deletingScreen = false;
      // Show error feedback
      alert('Failed to delete screen. Please try again.');
    }
  });
}

private loadCurrentPlaylistData(playlistId: string): void {
  this.playlistService.getPlaylist(playlistId).subscribe({
    next: (playlist) => {
      // Use the real playlist data directly
      this.currentPlaylistData = playlist;
    },
    error: (error) => {
      console.error('Error loading playlist data:', error);
      this.currentPlaylistData = null;
    }
  });
}

// Add a method to refresh current playlist data
private refreshCurrentPlaylistData(): void {
  if (this.screen && this.screen.current_playlist) {
    this.loadCurrentPlaylistData(this.screen.current_playlist);
  } else {
    this.currentPlaylistData = null;
  }
}

// Check if screen is offline based on last ping (more than 5 minutes ago)
isScreenOffline(): boolean {
  if (!this.screen || !this.screen.last_ping) return true;
  
  // Parse the last_ping date
  const lastPing = new Date(this.screen.last_ping);
  const now = new Date();
  
  // Check if lastPing is a valid date
  if (isNaN(lastPing.getTime())) return true;
  
  // Calculate the difference in minutes
  const diffInMinutes = (now.getTime() - lastPing.getTime()) / (1000 * 60);
  
  // Consider offline if last ping was more than 5 minutes ago
  return diffInMinutes > 5;
}

// Handle schedule updates from calendar component (drag resizing)
handleScheduleUpdated(schedule: Schedule): void {
  if (!this.screen || !this.screen.schedule) return;

  // Parse the schedule ID to get screen ID and actual schedule ID
  // The schedule ID format is: {screenId}___{playlist_id}___{start_time}___{end_time}
  const idParts = schedule.id.split('___');
  
  if (idParts.length < 4) {
    console.error('Could not parse schedule ID:', schedule.id);
    return;
  }
  
  const screenId = idParts[0];
  const actualScheduleId = idParts.slice(1).join('___'); // Join the rest to reconstruct the original ID

  // Verify that this schedule belongs to the current screen
  if (screenId !== this.screen.id) {
    console.error('Schedule does not belong to current screen:', schedule.id);
    return;
  }

  // Find the index of the schedule to update
  // We need to match based on the full ID
  const scheduleIndex = this.screen.schedule.upcoming.findIndex(upcoming => 
    `${upcoming.playlist_id}___${upcoming.start_time}___${upcoming.end_time}` === actualScheduleId
  );

  if (scheduleIndex === -1) {
    console.error('Could not find schedule to update:', actualScheduleId);
    return;
  }

  const playlistSchedule = convertCalendarScheduleToScreenFormat({
    ...schedule,
    id: actualScheduleId // Use the actual schedule ID for conversion
  });
  
  if (this.screen.schedule.upcoming && this.screen.schedule.upcoming[scheduleIndex]) {
    const updatedUpcoming = [...this.screen.schedule.upcoming];
    updatedUpcoming[scheduleIndex] = {
      ...updatedUpcoming[scheduleIndex],
      start_time: playlistSchedule.start_time,
      end_time: playlistSchedule.end_time,
    };

    this.screenService.updateScreen(this.screen.id, {
      schedule: {
        current: this.screen.schedule.current,
        upcoming: updatedUpcoming
      }
    }).subscribe({
      next: (updatedScreen) => {
        this.screen = updatedScreen;
        this.refreshCurrentPlaylistData();
      },
      error: (err) => {
        console.error('Error updating screen schedule', err);
      }
    });
  }
}

  addScheduleEntry(): void {
    const newSchedule: Schedule = {
      id: Date.now().toString(),
      startTime: '09:00',
      endTime: '10:00',
      playlistName: 'New Playlist',
      duration: 60,
      repeat: 'daily',
      status: 'inactive'
    };
    this.schedules.push(newSchedule);
  }

  removeSchedule(index: number): void {
    this.schedules.splice(index, 1);
  }

  onScheduleUpdated(schedule: Schedule): void {
    const idParts = schedule.id.split('___');
    if (idParts.length < 4) {
      console.error('Could not parse schedule ID for update:', schedule.id);
      return;
    }

    const screenId = idParts[0];
    const actualScheduleId = idParts.slice(1).join('___');

    if (this.screen && screenId === this.screen.id && this.screen.schedule) {
      const scheduleIndex = this.screen.schedule.upcoming.findIndex(upcoming => {
        const upcomingId = `${upcoming.playlist_id}___${upcoming.start_time}___${upcoming.end_time}`;
        return upcomingId === actualScheduleId;
      });

      if (scheduleIndex !== -1) {
        const updatedUpcoming = [...this.screen.schedule.upcoming];
        const playlistSchedule = convertCalendarScheduleToScreenFormat({
          ...schedule,
          id: actualScheduleId
        });

        updatedUpcoming[scheduleIndex] = {
          ...updatedUpcoming[scheduleIndex],
          start_time: playlistSchedule.start_time,
          end_time: playlistSchedule.end_time,
          days_of_week: playlistSchedule.days_of_week
        };

        this.screenService.updateScreen(screenId, {
          schedule: {
            current: this.screen.schedule.current,
            upcoming: updatedUpcoming
          }
        }).subscribe(() => {
          console.log('Schedule updated successfully');
          this.refreshSchedulesData();
        });
      } else {
        console.error('Could not find schedule to update:', actualScheduleId);
      }
    }
  }

  // Method to refresh schedules data after an update
  private refreshSchedulesData(): void {
    // If a specific screen is selected, refresh data for that screen
    if (this.screen) {
      this.screenService.getScreen(this.screen.id).subscribe(screen => {
        if (screen) {
          this.screen = screen;
          this.loadPlaylistNames();
        }
      });
    } 
  }

  // Toggle available playlist expansion
  toggleAvailablePlaylist(playlistId: string): void {
    this.expandedPlaylistId = this.expandedPlaylistId === playlistId ? null : playlistId;
  }

  // Get expanded playlists as a Set for the playlist panel component
  getExpandedPlaylistsSet(): Set<string> {
    return this.expandedPlaylistId ? new Set([this.expandedPlaylistId]) : new Set();
  }

  onMediaDragStart(event: DragEvent, media: Media): void {
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'copy';
      event.dataTransfer.setData('text/plain', JSON.stringify(media));
    }
  }

  onMediaDragEnd(event: DragEvent): void {
    // Placeholder for drag end logic
  }

  onPlaylistDragOver(event: DragEvent, playlistId: string): void {
    event.preventDefault();
    event.stopPropagation();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'copy';
    }
    const playlistElement = document.querySelector(`[data-playlist-id="${playlistId}"]`);
    if (playlistElement) {
      playlistElement.classList.add('playlist-drop-zone-active');
    }
  }

  onPlaylistDragLeave(event: DragEvent, playlistId: string): void {
    event.preventDefault();
    event.stopPropagation();
    const playlistElement = document.querySelector(`[data-playlist-id="${playlistId}"]`);
    if (playlistElement) {
      playlistElement.classList.remove('playlist-drop-zone-active');
    }
  }

  onPlaylistDrop(event: DragEvent, playlistId: string): void {
    event.preventDefault();
    event.stopPropagation();
    if (event.dataTransfer) {
      try {
        const mediaData = event.dataTransfer.getData('text/plain');
        if (mediaData) {
          const media: Media = JSON.parse(mediaData);
          this.addMediaToPlaylist(playlistId, media);
        }
      } catch (error) {
        console.error('Error parsing dropped media data:', error);
      }
    }
  }

  addMediaToPlaylist(playlistId: string, media: Media): void {
    this.playlistService.getPlaylist(playlistId).subscribe(playlist => {
      if (playlist) {
        const updatedItems = [...(playlist.items || []), {
          id: media.id,
          name: media.name,
          type: media.type,
          duration: media.duration || 10,
          content: {
            url: media.url,
            thumbnail: media.thumbnail_url
          },
          settings: {
            transition: 'fade',
            transitionDuration: 0.5,
            scaling: 'fit',
            muted: false,
            loop: false
          }
        } as PlaylistItemType];

        const updatedPlaylist = { ...playlist, items: updatedItems };

        this.playlistService.updatePlaylist(playlistId, updatedPlaylist).subscribe(() => {
          this.loadPlaylists(); // Refresh playlists
        });
      }
    });
  }

  

  dropPlaylistItem(event: CdkDragDrop<PlaylistItemType[]>, playlist: Playlist): void {
    if (event.previousIndex !== event.currentIndex) {
      const items = [...playlist.items];
      moveItemInArray(items, event.previousIndex, event.currentIndex);
      const updatedPlaylist = { ...playlist, items };
      this.playlistService.updatePlaylist(playlist.id, updatedPlaylist).subscribe(() => {
        this.loadPlaylists();
      });
    }
  }

  removeItemFromAvailablePlaylist(playlistId: string, itemIndex: number): void {
    this.playlistService.getPlaylist(playlistId).subscribe(playlist => {
      if (playlist) {
        const updatedItems = [...playlist.items];
        updatedItems.splice(itemIndex, 1);
        const updatedPlaylist = { ...playlist, items: updatedItems };
        this.playlistService.updatePlaylist(playlistId, updatedPlaylist).subscribe(() => {
          this.loadPlaylists();
        });
      }
    });
  }

  onPlaylistItemHover(item: any, event: MouseEvent): void {
    // Placeholder for hover logic
  }

  onPlaylistItemLeave(): void {
    // Placeholder for hover logic
  }

  openColorPicker(playlist: Playlist): void {
    // Placeholder for color picker logic
  }

  

  onPlaylistDragEnd(event: DragEvent): void {
    // Placeholder for drag end logic
  }

  onPlaylistDropped(event: { playlist: Playlist, timeSlot: { hour: number, minute: number } }): void {
    if (!this.screen) {
      console.warn('No screen selected, cannot schedule playlist');
      return;
    }

    const { playlist, timeSlot } = event;
    const formattedTime = `${timeSlot.hour.toString().padStart(2, '0')}:${timeSlot.minute.toString().padStart(2, '0')}`;
    const endHour = (timeSlot.hour + 1) % 24;
    const formattedEndTime = `${endHour.toString().padStart(2, '0')}:${timeSlot.minute.toString().padStart(2, '0')}`;

    const newSchedule: PlaylistScheduleBase = {
      playlist_id: playlist.id,
      start_time: formattedTime,
      end_time: formattedEndTime,
      priority: 1,
      days_of_week: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    };

    const existingSchedules = this.screen.schedule?.upcoming || [];
    const updatedSchedules = [...existingSchedules, newSchedule];

    this.screenService.updateScreen(this.screen.id, {
      schedule: {
        current: this.screen.schedule?.current || null,
        upcoming: updatedSchedules
      }
    }).subscribe(() => {
      this.refreshSchedulesData();
    });
  }
}
